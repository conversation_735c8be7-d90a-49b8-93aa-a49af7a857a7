#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

version: "3.8"

services:
  ## Eureka
  eureka:
    image: bitinit/eureka
    env_file:
      - ci/pod/eureka/env/common.env
    restart: unless-stopped
    ports:
      - "8761:8761"

  ## Consul
  consul_1:
    image: consul:1.7
    restart: unless-stopped
    ports:
      - "8500:8500"
    command: [ "consul", "agent", "-server", "-bootstrap-expect=1", "-client", "0.0.0.0", "-log-level", "info", "-data-dir=/consul/data", "-enable-script-checks" ]
    networks:
      consul_net:

  consul_2:
    image: consul:1.7
    restart: unless-stopped
    ports:
      - "8600:8500"
    command: [ "consul", "agent", "-server", "-bootstrap-expect=1", "-client", "0.0.0.0", "-log-level", "info", "-data-dir=/consul/data", "-enable-script-checks" ]
    networks:
      consul_net:

  consul_3:
    image: hashicorp/consul:1.16.2
    restart: unless-stopped
    ports:
      - "8502:8500"
    command: [ "consul", "agent", "-server", "-bootstrap-expect=1", "-client", "0.0.0.0", "-log-level", "info", "-data-dir=/consul/data", "-enable-script-checks", "-ui", "-hcl", "acl = {\nenabled = true\ndefault_policy = \"deny\"\nenable_token_persistence = true\ntokens = {\nagent = \"2b778dd9-f5f1-6f29-b4b4-9a5fa948757a\"\n}}" ]
    networks:
      consul_net:

  ## Consul cluster
  consul_node_1:
    image: consul:1.7
    restart: unless-stopped
    ports:
      - "9500:8500"
      - "8300:8300"
      - "8301:8301"
      - "8302:8302"
      - "9600:8600"
    command: [ "consul", "agent", "-server", "-bootstrap-expect=1", "-bind", "0.0.0.0", "-client", "0.0.0.0", "-node", "node-1", "-log-level", "info", "-data-dir=/consul/data", "-enable-script-checks" ]
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:8500/"]
        interval: 10s
        timeout: 10s
        retries: 5
    networks:
      consul_cluster_net:
        aliases:
          - consul.cluster

  consul_node_2:
    image: consul:1.7
    restart: unless-stopped
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    ports:
      - "9501:8500"
    command: [ "consul", "agent", "-server", "-bind", "0.0.0.0", "-client", "0.0.0.0", "-retry-join", "consul.cluster", "-node", "node-2", "-log-level", "info", "-data-dir=/consul/data", "-enable-script-checks" ]
    depends_on:
      consul_node_1:
        condition: service_healthy
    networks:
      consul_cluster_net:
        aliases:
          - consul.cluster

  consul_node_3:
    image: consul:1.7
    restart: unless-stopped
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    ports:
      - "9502:8500"
    command: [ "consul", "agent", "-server", "-bind", "0.0.0.0", "-client", "0.0.0.0", "-retry-join", "consul.cluster", "-node", "node-3", "-log-level", "info", "-data-dir=/consul/data", "-enable-script-checks" ]
    depends_on:
      consul_node_1:
        condition: service_healthy
    networks:
      consul_cluster_net:
        aliases:
          - consul.cluster

  ## Nacos cluster
  nacos_auth:
    hostname: nacos1
    image: nacos/nacos-server:1.4.1
    env_file:
      - ci/pod/nacos/env/common.env
    environment:
      NACOS_AUTH_ENABLE: "true"
    restart: unless-stopped
    ports:
      - "8848:8848"
    networks:
      nacos_net:

  nacos_no_auth:
    hostname: nacos2
    image: nacos/nacos-server:1.4.1
    env_file:
      - ci/pod/nacos/env/common.env
    restart: unless-stopped
    ports:
      - "8858:8848"
    networks:
      nacos_net:

  nacos_server_health_check:
    build:
      context: ci/pod/nacos/healthcheck
      dockerfile: Dockerfile
    environment:
      CHECK_URI: "http://nacos2:8848/nacos/v1/ns/service/list?pageNo=1&pageSize=2"
    tty: true
    # debug healthcheck script
#    volumes:
#     - ./ci/pod/nacos/healthcheck/nacos-server-healthcheck.sh:/nacos-server-healthcheck.sh
    healthcheck:
      test: [ "CMD", "bash", "/nacos-server-healthcheck.sh" ]
      interval: 5s
      timeout: 5s
      retries: 60
      start_period: 10s
    networks:
      nacos_net:

  nacos_service_health_check:
    build:
      context: ci/pod/nacos/healthcheck
      dockerfile: Dockerfile
    # debug healthcheck script
#    volumes:
#     - ./ci/pod/nacos/healthcheck/nacos-service-healthcheck.sh:/nacos-service-healthcheck.sh
    tty: true
    healthcheck:
      test: [ "CMD", "bash", "/nacos-service-healthcheck.sh" ]
      interval: 5s
      timeout: 30s
      retries: 60
      start_period: 10s
    networks:
      nacos_net:

  ### Nacos services
  nacos-service1:
    build:
      context: ci/pod/nacos/service
      dockerfile: Dockerfile
    env_file:
      - ci/pod/nacos/env/service.env
    environment:
      SUFFIX_NUM: 1
    restart: unless-stopped
    ports:
      - "18001:18001"
    depends_on:
      nacos_server_health_check:
        condition: service_healthy
    networks:
      nacos_net:

  nacos-service2:
    build:
      context: ci/pod/nacos/service
      dockerfile: Dockerfile
    env_file:
      - ci/pod/nacos/env/service.env
    environment:
      SUFFIX_NUM: 2
    restart: unless-stopped
    ports:
      - "18002:18001"
    depends_on:
      nacos_server_health_check:
        condition: service_healthy
    networks:
      nacos_net:

  nacos-service3:
    build:
      context: ci/pod/nacos/service
      dockerfile: Dockerfile
    env_file:
      - ci/pod/nacos/env/service.env
    environment:
      SUFFIX_NUM: 1
      NAMESPACE: test_ns
    restart: unless-stopped
    ports:
      - "18003:18001"
    depends_on:
      nacos_server_health_check:
        condition: service_healthy
    networks:
      nacos_net:

  nacos-service4:
    build:
      context: ci/pod/nacos/service
      dockerfile: Dockerfile
    env_file:
      - ci/pod/nacos/env/service.env
    environment:
      SUFFIX_NUM: 1
      GROUP: test_group
    restart: unless-stopped
    ports:
      - "18004:18001"
    depends_on:
      nacos_server_health_check:
        condition: service_healthy
    networks:
      nacos_net:

  nacos-service5:
    build:
      context: ci/pod/nacos/service
      dockerfile: Dockerfile
    env_file:
      - ci/pod/nacos/env/service.env
    environment:
      SUFFIX_NUM: 1
      GROUP: test_group
      NAMESPACE: test_ns
    restart: unless-stopped
    ports:
      - "18005:18001"
    depends_on:
      nacos_server_health_check:
        condition: service_healthy
    networks:
      nacos_net:

  nacos-service6:
    build:
      context: ci/pod/nacos/service
      dockerfile: Dockerfile
    env_file:
      - ci/pod/nacos/env/service.env
    environment:
      SUFFIX_NUM: 3
      GROUP: test_group2
      NAMESPACE: test_ns
    restart: unless-stopped
    ports:
      - "18006:18001"
    depends_on:
      nacos_server_health_check:
        condition: service_healthy
    networks:
      nacos_net:

  nacos-service7:
    build:
      context: ci/pod/nacos/service
      dockerfile: Dockerfile
    env_file:
      - ci/pod/nacos/env/service.env
    environment:
      SUFFIX_NUM: 4
      GROUP: test_group
      NAMESPACE: test_ns2
    restart: unless-stopped
    ports:
      - "18007:18001"
    depends_on:
      nacos_server_health_check:
        condition: service_healthy
    networks:
      nacos_net:


networks:
  consul_cluster_net:
  consul_net:
  nacos_net:
