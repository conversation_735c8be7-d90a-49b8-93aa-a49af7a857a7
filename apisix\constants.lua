--
-- Licensed to the Apache Software Foundation (ASF) under one or more
-- contributor license agreements.  See the NOTICE file distributed with
-- this work for additional information regarding copyright ownership.
-- The ASF licenses this file to You under the Apache License, Version 2.0
-- (the "License"); you may not use this file except in compliance with
-- the License.  You may obtain a copy of the License at
--
--     http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing, software
-- distributed under the License is distributed on an "AS IS" BASIS,
-- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
-- See the License for the specific language governing permissions and
-- limitations under the License.
--
return {
    RPC_ERROR = 0,
    RPC_PREPARE_CONF = 1,
    RPC_HTTP_REQ_CALL = 2,
    RPC_EXTRA_INFO = 3,
    RPC_HTTP_RESP_CALL = 4,
    HTTP_ETCD_DIRECTORY = {
        ["/upstreams"] = true,
        ["/plugins"] = true,
        ["/ssls"] = true,
        ["/stream_routes"] = true,
        ["/plugin_metadata"] = true,
        ["/routes"] = true,
        ["/services"] = true,
        ["/consumers"] = true,
        ["/global_rules"] = true,
        ["/protos"] = true,
        ["/plugin_configs"] = true,
        ["/consumer_groups"] = true,
        ["/secrets"] = true,
    },
    STREAM_ETCD_DIRECTORY = {
        ["/upstreams"] = true,
        ["/services"] = true,
        ["/plugins"] = true,
        ["/ssls"] = true,
        ["/stream_routes"] = true,
        ["/plugin_metadata"] = true,
    },
}
