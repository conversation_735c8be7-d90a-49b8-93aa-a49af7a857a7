name: CI Tars

on:
  push:
    branches: [ master, 'release/**' ]
    paths-ignore:
      - 'docs/**'
      - '**/*.md'
  pull_request:
    branches: [ master, 'release/**' ]
    paths-ignore:
      - 'docs/**'
      - '**/*.md'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref == 'refs/heads/master' && github.run_number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  tars:
    strategy:
      fail-fast: false
      matrix:
        platform:
          - ubuntu-20.04
        os_name:
          - linux_openresty

    runs-on: ${{ matrix.platform }}
    timeout-minutes: 15
    env:
      SERVER_NAME: ${{ matrix.os_name }}
      OPENRESTY_VERSION: default

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup Tars MySql
        run: |
          docker run -d -p 3306:3306 -v $PWD/t/tars/conf/tars.sql:/docker-entrypoint-initdb.d/tars.sql -e MYSQL_ROOT_PASSWORD=tars2022 mysql:5.7

      - name: Linux Install
        run: |
          sudo ./ci/${{ matrix.os_name }}_runner.sh before_install
          sudo --preserve-env=OPENRESTY_VERSION ./ci/${{ matrix.os_name }}_runner.sh do_install

      - name: Run test cases
        run: |
          ./ci/tars-ci.sh run_case
