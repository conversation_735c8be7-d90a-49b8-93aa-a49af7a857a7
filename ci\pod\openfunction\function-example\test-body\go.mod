module example.com/hello

go 1.17

require github.com/OpenFunction/functions-framework-go v0.3.0

require (
	github.com/SkyAPM/go2sky v1.4.1 // indirect
	github.com/cloudevents/sdk-go/v2 v2.4.1 // indirect
	github.com/dapr/dapr v1.6.0 // indirect
	github.com/dapr/go-sdk v1.3.1 // indirect
	github.com/go-logr/logr v1.2.0 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/google/uuid v1.3.0 // indirect
	github.com/json-iterator/go v1.1.11 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.1 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.7.0 // indirect
	go.uber.org/zap v1.19.1 // indirect
	golang.org/x/net v0.0.0-20211015210444-4f30a5c0130f // indirect
	golang.org/x/sys v0.0.0-20211019181941-9d821ace8654 // indirect
	golang.org/x/text v0.3.7 // indirect
	google.golang.org/genproto v0.0.0-20210831024726-fe130286e0e2 // indirect
	google.golang.org/grpc v1.40.0 // indirect
	google.golang.org/protobuf v1.33.0 // indirect
	gopkg.in/yaml.v3 v3.0.0 // indirect
	k8s.io/klog/v2 v2.30.0 // indirect
	skywalking.apache.org/repo/goapi v0.0.0-20220401015832-2c9eee9481eb // indirect
)
