name: <PERSON>

on:
  push:
    paths:
      - "docs/**"
      - "**/*.md"
      - ".github/workflows/doc-lint.yml"
  pull_request:
    branches: [master, "release/**"]
    paths:
      - "docs/**"
      - "**/*.md"
      - ".github/workflows/doc-lint.yml"

permissions:
  contents: read

jobs:
  markdownlint:
    name: 🍇 Markdown
    runs-on: ubuntu-latest
    timeout-minutes: 1
    steps:
      - uses: actions/checkout@v4
      - name: 🚀 Use Node.js
        uses: actions/setup-node@v4.0.2
        with:
          node-version: "12.x"
      - run: npm install -g markdownlint-cli@0.25.0
      - run: markdownlint '**/*.md'
      - name: check category
        run: |
          ./utils/check-category.py
      - name: check Chinese doc
        run: |
          sudo pip3 install zhon
          ./utils/fix-zh-doc-segment.py > \
            /tmp/check.log 2>&1 || (cat /tmp/check.log && exit 1)
          if grep "find broken newline in file: " /tmp/check.log; then
            cat /tmp/check.log
            echo "Newline can't appear in the middle of Chinese sentences."
            echo "You need to run ./utils/fix-zh-doc-segment.py to fix them."
            exit 1
          fi

  Chinse-Copywriting-lint:
    name: Chinese Copywriting
    runs-on: ubuntu-latest
    timeout-minutes: 1
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive
      - name: Check Chinese copywriting
        uses: ./.github/actions/autocorrect
        with:
          args: autocorrect --lint --no-diff-bg-color ./docs/zh/latest/
