#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

version: "3.8"

services:
  ## Redis
  apisix_redis:
    # The latest image is the latest stable version
    image: redis:latest
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      apisix_net:

  ## kafka-cluster
  zookeeper-server1:
    image: bitnami/zookeeper:3.6.0
    env_file:
      - ci/pod/kafka/zookeeper-server/env/common.env
    restart: unless-stopped
    ports:
      - "2181:2181"
    networks:
      kafka_net:

  zookeeper-server2:
    image: bitnami/zookeeper:3.6.0
    env_file:
      - ci/pod/kafka/zookeeper-server/env/common.env
    restart: unless-stopped
    ports:
      - "12181:12181"
    networks:
      kafka_net:

  kafka-server1:
    image: bitnami/kafka:2.8.1
    env_file:
      - ci/pod/kafka/kafka-server/env/last.env
    environment:
      KAFKA_CFG_ZOOKEEPER_CONNECT: zookeeper-server1:2181
    restart: unless-stopped
    ports:
      - "9092:9092"
      - "9093:9093"
      - "9094:9094"
    depends_on:
      - zookeeper-server1
      - zookeeper-server2
    networks:
      kafka_net:
    volumes:
      - ./ci/pod/kafka/kafka-server/kafka_jaas.conf:/opt/bitnami/kafka/config/kafka_jaas.conf:ro
      - ./ci/pod/kafka/kafka-server/selfsigned.jks:/opt/bitnami/kafka/config/certs/kafka.keystore.jks:ro
      - ./ci/pod/kafka/kafka-server/selfsigned.jks:/opt/bitnami/kafka/config/certs/kafka.truststore.jks:ro

  kafka-server2:
    image: bitnami/kafka:2.8.1
    env_file:
      - ci/pod/kafka/kafka-server/env/last.env
    environment:
      KAFKA_CFG_ZOOKEEPER_CONNECT: zookeeper-server2:2181
    restart: unless-stopped
    ports:
      - "19092:9092"
      - "19093:9093"
      - "19094:9094"
    depends_on:
      - zookeeper-server1
      - zookeeper-server2
    networks:
      kafka_net:
    volumes:
      - ./ci/pod/kafka/kafka-server/kafka_jaas.conf:/opt/bitnami/kafka/config/kafka_jaas.conf:ro
      - ./ci/pod/kafka/kafka-server/selfsigned.jks:/opt/bitnami/kafka/config/certs/kafka.keystore.jks:ro
      - ./ci/pod/kafka/kafka-server/selfsigned.jks:/opt/bitnami/kafka/config/certs/kafka.truststore.jks:ro


networks:
  apisix_net:
  kafka_net:
